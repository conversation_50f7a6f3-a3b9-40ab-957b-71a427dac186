FROM public.ecr.aws/lambda/python:3.12

# Copy the requirements.txt file into the container
COPY requirements.txt  .

# Install dependencies from requirements.txt
RUN pip install --no-cache-dir -r requirements.txt

# Copy your application code
COPY image_attribute_extractor_lambda_hackathon /var/task/image_attribute_extractor_lambda_hackathon

# Set working directory
WORKDIR /var/task

# Set the Lambda Runtime Interface Client as entrypoint
#ENTRYPOINT [ "/usr/local/bin/python", "-m", "awslambdaric" ]
ENTRYPOINT [ "/var/lang/bin/python3.12", "-m", "awslambdaric" ]

# Set the handler function
CMD [ "image_attribute_extractor_lambda_hackathon.main.handler" ]


