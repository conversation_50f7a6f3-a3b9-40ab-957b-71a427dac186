import json
import asyncio
import aiohttp
import traceback
import re  # Although parse_images isn't used directly on this input, keeping imports consistent

# API endpoint
API_URL = 'https://vulzh3m6jg3luliglnwoxcngv40spkck.lambda-url.us-east-1.on.aws/'
CONCURRENT_CALLS = 5  # Limit to 5 calls in parallel


# Async function to make API calls
async def make_api_call(session, data_input, semaphore):
    async with semaphore:  # Acquire a semaphore slot to limit concurrency
        # The data_input directly from the JSONL file already contains
        # 'images' (as a list), 'SKU', 'category', 'Title', 'description',
        # 'requestType', and 'response_type'.
        input_payload = data_input

        response_data = {
            "Error": None,
            "input": input_payload,
            "output": None
        }

        try:
            # Make the POST request with the prepared JSON payload
            async with session.post(API_URL, json=input_payload, headers={'Content-Type': 'application/json'},
                                    timeout=30) as response:
                response_text = await response.text()  # Get raw response text for robust error handling

                if response.status == 200:
                    try:
                        response_json = json.loads(response_text)
                        response_data["output"] = response_json
                    except json.JSONDecodeError:
                        response_data[
                            "Error"] = f"JSONDecodeError: Could not decode response from API. Status: {response.status}. Raw response (first 500 chars): {response_text[:500]}..."
                else:
                    response_data[
                        "Error"] = f"API request failed with status {response.status}. Response (first 500 chars): {response_text[:500]}..."
        except aiohttp.ClientError as e:
            response_data["Error"] = f"ClientError: {e}"
        except asyncio.TimeoutError:
            response_data["Error"] = "TimeoutError: API request timed out after 30 seconds."
        except Exception as e:
            # Catch any other unexpected errors during the request
            response_data["Error"] = f"An unexpected error occurred: {e}. Traceback: {traceback.format_exc()}"

        return response_data


# Main async function to orchestrate the calls
async def main():
    # Retrieve the content of the JSONL file generated previously
    # This specifically fetches the file generated in the previous step by its content tag.
    try:
        # Replace 'code-generated-file-0-1752138613653154295' with the actual file tag
        # if you are running this in a new session and have a different tag for your file.
        jsonl_content = get_file_content(file_tag='')
    except Exception as e:
        print(
            f"Error reading input JSONL file. Ensure 'output_with_static_details.jsonl' was successfully generated and its file tag is correct: {e}")
        return

    # Parse each line of the JSONL content into a list of dictionaries
    api_inputs = []
    for line in jsonl_content.strip().split('\n'):
        if line.strip():  # Skip empty lines
            try:
                # Each line is already a complete JSON object including static fields
                api_inputs.append(json.loads(line))
            except json.JSONDecodeError as e:
                print(f"Error decoding JSON line (skipping): {line[:100]}... Error: {e}")
                continue

    if not api_inputs:
        print("No valid input data found in the JSONL file. Exiting.")
        return

    semaphore = asyncio.Semaphore(CONCURRENT_CALLS)  # Initialize the semaphore

    # Create a single aiohttp ClientSession for efficient connection management
    async with aiohttp.ClientSession() as session:
        # Create a list of tasks (coroutines) for all API calls
        tasks = [make_api_call(session, data_item, semaphore) for data_item in api_inputs]

        # Run all tasks concurrently and wait for them to complete
        results = await asyncio.gather(*tasks)

    # Define the output file name for API call results
    output_results_filename = "api_call_results.jsonl"

    # Write the results to the new JSONL file
    with open(output_results_filename, 'w', encoding='utf-8') as f:
        for result in results:
            # Dump each result dictionary to a JSON string and write as a new line
            f.write(json.dumps(result, ensure_ascii=False) + '\n')

    print(f"\nAPI call results saved to '{output_results_filename}'.")


# This part ensures the asyncio event loop is run correctly,
# whether in an interactive environment or as a standalone script.
try:
    loop = asyncio.get_running_loop()
    if loop.is_running():
        # If a loop is already running, schedule main as a task
        asyncio.create_task(main())
    else:
        # No loop running, start a new one
        asyncio.run(main())
except RuntimeError:
    # This handles cases where get_running_loop() might raise an error if no loop is set
    asyncio.run(main())