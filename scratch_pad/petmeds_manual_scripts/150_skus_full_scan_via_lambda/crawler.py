
import json
import asyncio
import aiohttp
import traceback
import ssl

# API endpoint
API_URL = 'https://vulzh3m6jg3luliglnwoxcngv40spkck.lambda-url.us-east-1.on.aws/'
CONCURRENT_CALLS = 5  # Limit to 5 calls in parallel


# Async function to make API calls
async def make_api_call(session, data_input, semaphore):
    async with semaphore:  # Acquire a semaphore slot to limit concurrency
        # The data_input directly from the JSONL file already contains
        # 'images' (as a list), 'SKU', 'category', 'Title', 'description',
        # 'requestType', and 'response_type'.
        input_payload = data_input
        sku = input_payload.get('SKU', 'Unknown SKU')

        print(f"Processing SKU: {sku}")

        response_data = {
            "Error": None,
            "input": input_payload,
            "output": None
        }

        try:
            # Make the POST request with the prepared JSON payload (no timeout)
            async with session.post(API_URL, json=input_payload, headers={'Content-Type': 'application/json'}) as response:
                response_text = await response.text()  # Get raw response text for robust error handling

                if response.status == 200:
                    try:
                        response_json = json.loads(response_text)
                        response_data["output"] = response_json
                        print(f"✓ Successfully processed SKU: {sku}")
                    except json.JSONDecodeError:
                        response_data[
                            "Error"] = f"JSONDecodeError: Could not decode response from API. Status: {response.status}. Raw response (first 500 chars): {response_text[:500]}..."
                        print(f"✗ JSON decode error for SKU: {sku}")
                else:
                    response_data[
                        "Error"] = f"API request failed with status {response.status}. Response (first 500 chars): {response_text[:500]}..."
                    print(f"✗ API request failed for SKU: {sku} with status {response.status}")
        except aiohttp.ClientError as e:
            response_data["Error"] = f"ClientError: {e}"
            print(f"✗ Client error for SKU: {sku} - {e}")
        except Exception as e:
            # Catch any other unexpected errors during the request
            response_data["Error"] = f"An unexpected error occurred: {e}. Traceback: {traceback.format_exc()}"
            print(f"✗ Unexpected error for SKU: {sku} - {e}")

        return response_data


# Main async function to orchestrate the calls
async def main():
    # Read the input JSONL file
    input_file = "150_skus_input.jsonl"

    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            jsonl_content = f.read()
    except Exception as e:
        print(f"Error reading input file {input_file}: {e}")
        return

    # Parse each line of the JSONL content into a list of dictionaries
    api_inputs = []
    for line in jsonl_content.strip().split('\n'):
        if line.strip():  # Skip empty lines
            try:
                api_inputs.append(json.loads(line))
            except json.JSONDecodeError as e:
                print(f"Error decoding JSON line (skipping): {line[:100]}... Error: {e}")
                continue

    if not api_inputs:
        print("No valid input data found in the JSONL file. Exiting.")
        return

    print(f"Found {len(api_inputs)} SKUs to process")
    print(f"Using {CONCURRENT_CALLS} concurrent calls")
    print("Starting API calls...")

    semaphore = asyncio.Semaphore(CONCURRENT_CALLS)  # Initialize the semaphore

    # Create SSL context that doesn't verify certificates (for Lambda URL)
    ssl_context = ssl.create_default_context()
    ssl_context.check_hostname = False
    ssl_context.verify_mode = ssl.CERT_NONE

    # Create a single aiohttp ClientSession for efficient connection management
    connector = aiohttp.TCPConnector(ssl=ssl_context)
    async with aiohttp.ClientSession(connector=connector) as session:
        # Create a list of tasks (coroutines) for all API calls
        tasks = [make_api_call(session, data_item, semaphore) for data_item in api_inputs]

        # Run all tasks concurrently and wait for them to complete
        print("Waiting for all API calls to complete...")
        results = await asyncio.gather(*tasks)

    # Count successful and failed results
    successful = sum(1 for result in results if result.get("Error") is None)
    failed = len(results) - successful

    print(f"\nProcessing complete!")
    print(f"Total SKUs processed: {len(results)}")
    print(f"Successful: {successful}")
    print(f"Failed: {failed}")

    # Define the output file name for API call results
    output_results_filename = "api_call_results.jsonl"

    # Write the results to the new JSONL file
    with open(output_results_filename, 'w', encoding='utf-8') as f:
        for result in results:
            # Dump each result dictionary to a JSON string and write as a new line
            f.write(json.dumps(result, ensure_ascii=False) + '\n')

    print(f"API call results saved to '{output_results_filename}'.")


# This part ensures the asyncio event loop is run correctly,
# whether in an interactive environment or as a standalone script.
try:
    loop = asyncio.get_running_loop()
    if loop.is_running():
        # If a loop is already running, schedule main as a task
        asyncio.create_task(main())
    else:
        # No loop running, start a new one
        asyncio.run(main())
except RuntimeError:
    # This handles cases where get_running_loop() might raise an error if no loop is set
    asyncio.run(main())